// FILE: lib/screens/popup.dart
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class CategoryPopup extends StatefulWidget {
  const CategoryPopup({super.key});

  @override
  State<CategoryPopup> createState() => _CategoryPopupState();
}

class _CategoryPopupState extends State<CategoryPopup> {
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _vendorController = TextEditingController();
  final TextEditingController _noteController = TextEditingController();
  String _selectedCategory = 'Food';
  final List<String> _categories = [
    'Food',
    'Grocery',
    'Stationary',
    'Clothes',
    'Travel',
    'Medical',
    'Others'
  ];

  void _saveTransaction() async {
    final uid = FirebaseAuth.instance.currentUser!.uid;
    await FirebaseFirestore.instance
        .collection("users/$uid/transactions")
        .add({
      "amount": double.tryParse(_amountController.text) ?? 0,
      "vendor": _vendorController.text,
      "category": _selectedCategory,
      "timestamp": DateTime.now(),
      "note": _noteController.text,
    });
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text("Add Expense"),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _amountController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(labelText: "Amount"),
          ),
          TextField(
            controller: _vendorController,
            decoration: const InputDecoration(labelText: "Vendor"),
          ),
          DropdownButton<String>(
            value: _selectedCategory,
            items: _categories.map((String value) {
              return DropdownMenuItem<String>(
                value: value,
                child: Text(value),
              );
            }).toList(),
            onChanged: (val) => setState(() => _selectedCategory = val!),
          ),
          TextField(
            controller: _noteController,
            decoration: const InputDecoration(labelText: "Note"),
          ),
        ],
      ),
      actions: [
        TextButton(
            onPressed: () => Navigator.pop(context), child: const Text("Cancel")),
        ElevatedButton(onPressed: _saveTransaction, child: const Text("Save"))
      ],
    );
  }
}
