# Firebase Setup Instructions

## Prerequisites
1. Create a Firebase project at https://console.firebase.google.com
2. Enable Authentication and Firestore Database in your Firebase project

## Android Setup

1. **Add Android App to Firebase:**
   - Go to Project Settings in Firebase Console
   - Click "Add app" and select Android
   - Package name: `com.example.upi_expense_tracker`
   - App nickname: `UPI Expense Tracker`
   - Download `google-services.json`

2. **Place Configuration File:**
   - Copy `google-services.json` to `android/app/` directory

## iOS Setup

1. **Add iOS App to Firebase:**
   - Go to Project Settings in Firebase Console
   - Click "Add app" and select iOS
   - Bundle ID: `com.example.upiExpenseTracker`
   - App nickname: `UPI Expense Tracker`
   - Download `GoogleService-Info.plist`

2. **Place Configuration File:**
   - Copy `GoogleService-Info.plist` to `ios/Runner/` directory

## Enable Authentication

1. **In Firebase Console:**
   - Go to Authentication > Sign-in method
   - Enable "Google" sign-in provider
   - Add your app's SHA-1 fingerprint for Android

2. **Get SHA-1 Fingerprint (for Android):**
   ```bash
   cd android
   ./gradlew signingReport
   ```

## Enable Firestore Database

1. **In Firebase Console:**
   - Go to Firestore Database
   - Create database in test mode
   - Choose a location close to your users

## Security Rules for Firestore

Replace the default rules with:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId}/transactions/{document} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

## Important Notes

- The configuration files (`google-services.json` and `GoogleService-Info.plist`) contain sensitive information
- Add these files to your `.gitignore` if you plan to commit to version control
- For production, use proper security rules and enable App Check
