# UPI Expense Tracker

A Flutter application for tracking UPI expenses with Firebase integration, featuring Google Sign-In authentication and real-time data storage.

## Features

- 🔐 Google Sign-In Authentication
- 💰 Add and categorize expenses
- 📊 Analytics dashboard (coming soon)
- 🔥 Firebase Firestore for data storage
- 📱 Cross-platform (Android & iOS)

## Prerequisites

Before running this project, make sure you have:

1. **Flutter SDK** installed (version 3.0.0 or higher)
   - Download from: https://flutter.dev/docs/get-started/install
   - Verify installation: `flutter doctor`

2. **Firebase Project** set up
   - Create at: https://console.firebase.google.com
   - Enable Authentication and Firestore Database

3. **Development Environment**
   - Android Studio or VS Code
   - Android SDK (for Android development)
   - Xcode (for iOS development on macOS)

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
# Navigate to project directory
cd tracker

# Install Flutter dependencies
flutter pub get
```

### 2. Firebase Configuration

**Important:** You must complete Firebase setup before running the app.

1. Follow the detailed instructions in `firebase_setup.md`
2. Download and place the configuration files:
   - `google-services.json` → `android/app/`
   - `GoogleService-Info.plist` → `ios/Runner/`

### 3. Verify Setup

```bash
# Check if everything is configured correctly
flutter doctor

# Analyze the project for any issues
flutter analyze
```

## Running the Application

### Development Mode

```bash
# Run on connected device/emulator
flutter run

# Run on specific device
flutter devices  # List available devices
flutter run -d <device-id>

# Run with hot reload (default in debug mode)
flutter run --hot
```

### Platform-Specific Commands

```bash
# Android only
flutter run -d android

# iOS only (macOS required)
flutter run -d ios
```

### Build for Production

```bash
# Android APK
flutter build apk

# Android App Bundle (recommended for Play Store)
flutter build appbundle

# iOS (macOS required)
flutter build ios
```

## Project Structure

```
lib/
├── main.dart              # App entry point
├── screens/               # UI screens
│   ├── home.dart         # Main dashboard
│   ├── login.dart        # Authentication screen
│   ├── popup.dart        # Add expense dialog
│   └── analytics.dart    # Analytics dashboard
└── services/             # Business logic
    └── auth_service.dart # Authentication service
```

## Troubleshooting

### Common Issues

1. **Firebase not configured**
   ```
   Error: MissingPluginException(No implementation found for method...)
   ```
   - Solution: Complete Firebase setup in `firebase_setup.md`

2. **Dependencies not installed**
   ```
   Error: Target of URI doesn't exist
   ```
   - Solution: Run `flutter pub get`

3. **Android build fails**
   ```
   Error: Could not resolve all artifacts for configuration
   ```
   - Solution: Check Android SDK and build tools are installed

4. **iOS build fails (macOS only)**
   ```
   Error: CocoaPods not installed
   ```
   - Solution: Install CocoaPods: `sudo gem install cocoapods`

### Getting Help

- Run `flutter doctor` to diagnose setup issues
- Check `flutter analyze` for code issues
- Ensure all Firebase configuration files are in place
- Verify internet connection for Firebase services

## Development

### Adding New Features

1. Create new screens in `lib/screens/`
2. Add services in `lib/services/`
3. Update dependencies in `pubspec.yaml`
4. Run `flutter pub get` after adding dependencies

### Testing

```bash
# Run tests
flutter test

# Run integration tests
flutter drive --target=test_driver/app.dart
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is for educational purposes. Please ensure you comply with Firebase and Google Sign-In terms of service.
