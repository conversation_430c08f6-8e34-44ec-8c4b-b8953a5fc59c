# Quick Start Guide

## 🚀 Get Running in 5 Minutes

### Step 1: Install Flutter
```bash
# Check if Flutter is installed
flutter --version

# If not installed, download from: https://flutter.dev/docs/get-started/install
```

### Step 2: Install Dependencies
```bash
flutter pub get
```

### Step 3: Set Up Firebase (REQUIRED)
1. Go to https://console.firebase.google.com
2. Create a new project
3. Enable Authentication → Google Sign-In
4. Enable Firestore Database
5. Download config files:
   - `google-services.json` → place in `android/app/`
   - `GoogleService-Info.plist` → place in `ios/Runner/`

### Step 4: Configure Flutter SDK Path
Edit `android/local.properties` and add your Flutter SDK path:
```
flutter.sdk=C:\\path\\to\\your\\flutter
```

### Step 5: Run the App
```bash
# Connect a device or start an emulator
flutter devices

# Run the app
flutter run
```

## ⚠️ Important Notes

- **Firebase setup is mandatory** - the app won't work without it
- You need a physical device or emulator to test Google Sign-In
- For iOS development, you need macOS and Xcode

## 🔧 Troubleshooting

**App crashes on startup?**
- Check if Firebase config files are in the correct locations
- Run `flutter clean` then `flutter pub get`

**Build errors?**
- Run `flutter doctor` to check your setup
- Make sure Android SDK is properly installed

**Google Sign-In not working?**
- Verify SHA-1 fingerprint is added to Firebase (Android)
- Check if Google Sign-In is enabled in Firebase Console

Need more help? Check the full `README.md` file.
